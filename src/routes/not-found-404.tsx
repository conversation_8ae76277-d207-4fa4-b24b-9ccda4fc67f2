import { useTranslation } from "@/hooks/useTranslation";
import React from "react";

export default function NotFound404() {
  const { t } = useTranslation();
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-black px-4 py-12">
      <img
        src="/images/404-500-Disrupted.svg"
        alt="Agent Pathway Disrupted"
        className="w-48 md:w-72 lg:w-80 mb-8"
        draggable={false}
      />
      <div className="font-mono text-teal max-w-prose text-base md:text-lg text-left">
        <div className="text-lg md:text-xl font-bold mb-6 text-center">
          {t('pages.notFound.title')}
        </div>
        <p className="mb-4">{t('pages.notFound.greeting')}</p>
        <p className="mb-4">{t('pages.notFound.edgeMessage')}</p>
        <p className="mb-4">{t('pages.notFound.notThePage')}</p>
        <p className="mb-4">{t('pages.notFound.oldInternet')}</p>
        <p className="mb-4">{t('pages.notFound.weCallThem')}</p>
        <p className="mb-4">{t('pages.notFound.notChatbots')}</p>
        <p className="mb-4">{t('pages.notFound.everySoul')}</p>
        <p className="mb-4">{t('pages.notFound.buildingNewWeb')}</p>
        <p className="mb-4">{t('pages.notFound.ifDetour')}</p>
        <p className="mb-4">{t('pages.notFound.notLost')}</p>
        <p className="mb-4">{t('pages.notFound.everyMisclick')} </p>
        <p className="mt-8 font-bold">{t('pages.notFound.thinkForYourself')}</p>
      </div>
    </div>
  );
}
