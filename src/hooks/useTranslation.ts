import { useTranslation as useI18nTranslation } from 'react-i18next';

/**
 * Custom hook that wraps react-i18next's useTranslation hook
 * Provides type-safe access to translations with better developer experience
 */
export const useTranslation = () => {
  const { t, i18n } = useI18nTranslation();

  return {
    t,
    i18n,
    // Helper functions for common translation patterns
    changeLanguage: (lng: string) => i18n.changeLanguage(lng),
    currentLanguage: i18n.language,
    isLoading: !i18n.isInitialized,
  };
};

// Type-safe translation keys for better IDE support
export type TranslationKey =
  // Header translations
  | 'header.navigation.about'
  | 'header.navigation.thinkProtocol'
  | 'header.navigation.resources'
  | 'header.navigation.contact'
  | 'header.dropdown.about.team'
  | 'header.dropdown.about.partners'
  | 'header.dropdown.about.investors'
  | 'header.dropdown.thinkProtocol.thinkToken'
  | 'header.dropdown.thinkProtocol.thinkBuilders'
  | 'header.dropdown.resources.docs'
  | 'header.dropdown.resources.whitepaper'
  | 'header.dropdown.resources.faqs'
  | 'header.buttons.mintAnnouncement'
  | 'header.buttons.joinTheMovement'
  | 'header.buttons.claimThink'
  | 'header.countdown.foundersBonus'
  | 'header.footer.privacy'
  | 'header.footer.terms'
  | 'header.accessibility.openMainMenu'
  | 'header.accessibility.closeMenu'
  | 'header.socialMedia.twitter'
  | 'header.socialMedia.discord'
  | 'header.socialMedia.youtube'
  | 'header.socialMedia.magicEden'
  // Home page translations
  | 'pages.home.hero.title'
  | 'pages.home.hero.subtitle'
  | 'pages.home.features.title'
  | 'pages.home.features.subtitle'
  | 'pages.home.features.description'
  | 'pages.home.features.professionalAgents.title'
  | 'pages.home.features.professionalAgents.description'
  | 'pages.home.features.ownExperience.title'
  | 'pages.home.features.ownExperience.description'
  | 'pages.home.features.patentProtection.title'
  | 'pages.home.features.patentProtection.description'
  | 'pages.home.features.seamlessInteroperability.title'
  | 'pages.home.features.seamlessInteroperability.description'
  // About page translations
  | 'pages.about.title'
  | 'pages.about.hero.title'
  | 'pages.about.hero.subtitle'
  | 'pages.about.hero.mission'
  | 'pages.about.description.paragraph1'
  | 'pages.about.description.paragraph2'
  | 'pages.about.description.paragraph3'
  | 'pages.about.team.title'
  // Contact page translations
  | 'pages.contact.title'
  | 'pages.contact.subtitle'
  | 'pages.contact.form.fullName'
  | 'pages.contact.form.email'
  | 'pages.contact.form.twitterHandle'
  | 'pages.contact.form.message'
  | 'pages.contact.form.submit'
  | 'pages.contact.form.submitting'
  | 'pages.contact.errors.generic'
  // Dashboard translations
  | 'pages.dashboard.greeting'
  | 'pages.dashboard.greetingDefault'
  | 'pages.dashboard.greetingLoading'
  | 'pages.dashboard.greetingLoadingDefault'
  | 'pages.dashboard.yourRewards'
  | 'pages.dashboard.availableToStake'
  | 'pages.dashboard.claimAndRestake'
  | 'pages.dashboard.ineligible'
  | 'pages.dashboard.notImplemented'
  | 'pages.dashboard.foundersBonus'
  // Thinkubator translations
  | 'pages.thinkubator.hero.title'
  | 'pages.thinkubator.hero.season'
  | 'pages.thinkubator.hero.seasonTime'
  | 'pages.thinkubator.about.description'
  | 'pages.thinkubator.errors.notEnabled'
  // Not Found page translations
  | 'pages.notFound.title'
  | 'pages.notFound.subtitle'
  // FAQ page translations
  | 'pages.faq.title'
  // Blog page translations
  | 'pages.blog.title'
  // Investors page translations
  | 'pages.investors.title'
  | 'pages.investors.hero.title'
  | 'pages.investors.hero.description'
  // Partners page translations
  | 'pages.partners.title'
  | 'pages.partners.hero.title'
  | 'pages.partners.hero.description1'
  | 'pages.partners.hero.description2'
  | 'pages.partners.hero.linkText'
  // Claim page translations
  | 'pages.claim.title'
  | 'pages.claim.subtitle'
  | 'pages.claim.faqTitle'
  | 'pages.claim.benefitsTitle'
  | 'pages.claim.faqs.whatIsThink.question'
  | 'pages.claim.faqs.whatIsThink.answer'
  | 'pages.claim.faqs.howDifferentFromAsto.question'
  | 'pages.claim.faqs.howDifferentFromAsto.answer'
  | 'pages.claim.faqs.whoBehindThink.question'
  | 'pages.claim.faqs.whoBehindThink.answer'
  | 'pages.claim.faqs.howManyTokens.question'
  | 'pages.claim.faqs.howManyTokens.answer'
  | 'pages.claim.faqs.howCanIBuild.question'
  | 'pages.claim.faqs.howCanIBuild.answer'
  | 'pages.claim.faqs.canIStake.question'
  | 'pages.claim.faqs.canIStake.answer'
  | 'pages.claim.faqs.whatIsStaking.question'
  | 'pages.claim.faqs.whatIsStaking.answer'
  | 'pages.claim.faqs.whyCantWithdraw.question'
  | 'pages.claim.faqs.whyCantWithdraw.answer'
  | 'pages.claim.benefits.professionalAgents.title'
  | 'pages.claim.benefits.professionalAgents.description'
  | 'pages.claim.benefits.ownExperience.title'
  | 'pages.claim.benefits.ownExperience.description'
  | 'pages.claim.benefits.patentProtection.title'
  | 'pages.claim.benefits.patentProtection.description'
  | 'pages.claim.benefits.seamlessInteroperability.title'
  | 'pages.claim.benefits.seamlessInteroperability.description'
  // Dialog translations
  | 'dialogs.goldenTicket.title'
  | 'dialogs.goldenTicket.description'
  | 'dialogs.goldenTicket.viewTransaction'
  | 'dialogs.goldenTicket.etherscan'
  | 'dialogs.astoConversion.title'
  | 'dialogs.astoConversion.approving'
  | 'dialogs.astoConversion.approved'
  | 'dialogs.astoConversion.burning'
  | 'dialogs.astoConversion.success'
  | 'dialogs.astoConversion.error'
  | 'dialogs.astoConversion.swappingDisabled'
  | 'dialogs.astoConversion.swappingDisabledDescription'
  | 'dialogs.astoConversion.termsAgreement'
  | 'dialogs.astoConversion.termsOfService'
  | 'dialogs.astoConversion.privacyPolicy'
  | 'dialogs.astoConversion.youAreBurning'
  | 'dialogs.astoConversion.youAreReceiving'
  | 'dialogs.astoConversion.twoTransactions'
  | 'dialogs.astoConversion.approvingSpend'
  | 'dialogs.astoConversion.processingTransaction'
  | 'dialogs.astoConversion.burningForThink'
  | 'dialogs.astoConversion.approvalSuccessful'
  | 'dialogs.astoConversion.proceedToBurn'
  | 'dialogs.astoConversion.burnSuccessful'
  | 'dialogs.astoConversion.approveAsto'
  | 'dialogs.astoConversion.confirmBurn'
  | 'dialogs.astoConversion.transactionInProgress'
  | 'dialogs.astoConversion.viewTransaction'
  | 'dialogs.stake.titleStake'
  | 'dialogs.stake.titleUnstake'
  | 'dialogs.stake.stakingDisabled'
  | 'dialogs.stake.stakingDisabledDescription'
  | 'dialogs.stake.reachOutDiscord'
  | 'dialogs.stake.transactionSuccessful'
  | 'dialogs.stake.youHaveStaked'
  | 'dialogs.stake.stakeThink'
  | 'dialogs.stake.approvedForStaking'
  | 'dialogs.stake.transactionUnsuccessful'
  | 'dialogs.stake.errorOccurred'
  | 'dialogs.stake.youAreClaimingAll'
  | 'dialogs.stake.approvingAllowance'
  | 'dialogs.stake.allowanceApproved'
  | 'dialogs.stake.confirmingAllowance'
  | 'dialogs.stake.stakingThink'
  | 'dialogs.stake.canTakeMinutes'
  | 'dialogs.stake.youWillStake'
  | 'dialogs.stake.insufficientThink'
  | 'dialogs.stake.purchaseMoreThink'
  | 'dialogs.stake.inProgress'
  | 'dialogs.stake.approveAllowance'
  | 'dialogs.stake.tryAgain'
  | 'dialogs.stake.errorClickReload'
  | 'dialogs.stake.termsAgreement'
  | 'dialogs.stake.termsOfService'
  | 'dialogs.stake.privacyPolicy'
  | 'dialogs.stake.viewTransactionOn'
  | 'dialogs.stake.etherscan'
  | 'dialogs.claimStake.title'
  | 'dialogs.claimStake.claimingDisabled'
  | 'dialogs.claimStake.claimingDisabledDescription'
  | 'dialogs.claimStake.reachOutDiscord'
  | 'dialogs.claimStake.transactionSuccessfullySent'
  | 'dialogs.claimStake.transactionBeingProcessed'
  | 'dialogs.claimStake.transactionUnsuccessful'
  | 'dialogs.claimStake.errorOccurred'
  | 'dialogs.claimStake.youAreClaimingAll'
  | 'dialogs.claimStake.preparingClaim'
  | 'dialogs.claimStake.claimStakeInProgress'
  | 'dialogs.claimStake.claimInProgress'
  | 'dialogs.claimStake.canTakeMinutes'
  | 'dialogs.claimStake.youWillReceive'
  | 'dialogs.claimStake.cautionClaimed'
  | 'dialogs.claimStake.inProgress'
  | 'dialogs.claimStake.claimOnly'
  | 'dialogs.claimStake.claimAndStake'
  | 'dialogs.claimStake.tryAgain'
  | 'dialogs.claimStake.errorClickReload'
  | 'dialogs.claimStake.termsAgreement'
  | 'dialogs.claimStake.termsOfService'
  | 'dialogs.claimStake.privacyPolicy'
  | 'dialogs.claimStake.viewTransactionOn'
  | 'dialogs.claimStake.etherscan'
  // 404 Not Found page translations
  | 'pages.notFound.title'
  | 'pages.notFound.greeting'
  | 'pages.notFound.edgeMessage'
  | 'pages.notFound.notThePage'
  | 'pages.notFound.oldInternet'
  | 'pages.notFound.weCallThem'
  | 'pages.notFound.notChatbots'
  | 'pages.notFound.everySoul'
  | 'pages.notFound.buildingNewWeb'
  | 'pages.notFound.ifDetour'
  | 'pages.notFound.notLost'
  | 'pages.notFound.everyMisclick'
  | 'pages.notFound.thinkForYourself'
  // Common translations
  | 'common.buttons.applyNow'
  | 'common.buttons.learnMore'
  | 'common.buttons.continue'
  | 'common.buttons.submit'
  | 'common.buttons.cancel'
  | 'common.buttons.close'
  | 'common.buttons.save'
  | 'common.buttons.edit'
  | 'common.buttons.delete'
  | 'common.buttons.back'
  | 'common.buttons.next'
  | 'common.loading'
  | 'common.error'
  | 'common.success'
  | 'common.warning'
  | 'common.info';

export default useTranslation;
