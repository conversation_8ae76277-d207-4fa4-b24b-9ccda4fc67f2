import * as React from "react";
import { useTranslation } from "@/hooks/useTranslation";
import { Button } from "./button";

interface LanguageSwitcherProps {
  className?: string;
  option?: string;
}

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({ option, className }) => {
  const { changeLanguage, currentLanguage } = useTranslation();

  // contains the language object, including i18n code, name and flag emoji
  const languages = [
    { code: 'en', name: 'English', flag: "🇺🇸" },
    { code: 'es', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', flag: "🇪🇸" },
    { code: 'zh', name: '漢字', flag: "🇨🇳" },
  ];

  const handleLanguageChange = (languageCode: string) => {
    changeLanguage(languageCode);
  };

  if (option === "buttons") {
    return (
      <div className={`flex gap-2 ${className}`}>
        {languages.map((language) => (
          <Button
            key={language.code}
            variant={currentLanguage === language.code ? "outline" : "gradient"}
            size="sm"
            onClick={() => handleLanguageChange(language.code)}
            className="text-xs"
          >
            {language.flag} {language.name.toUpperCase()}
          </Button>
        ))}
      </div>
    );
  }

  return (
    <select
      value={currentLanguage}
      onChange={(ev) => handleLanguageChange(ev.target.value)}
      className={`cursor-pointer inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 rounded-full  border border-input border-white focus:border-white bg-black bg-background shadow-sm hover:bg-accent hover:text-accent-foreground text-xl ${className}`}
    >
      {languages.map((language) => (
        <option key={language.code} value={language.code}>
          {language.flag}
        </option>
      ))}
    </select>
  )
};

export { LanguageSwitcher };
